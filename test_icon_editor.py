#!/usr/bin/env python3
"""
测试图标编辑器删除功能的简单脚本
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from one_dragon.base.geometry.point import Point
from zzz_od.application.world_patrol.world_patrol_area import WorldPatrolLargeMapIcon
from zzz_od.gui.view.devtools.icon_editor_dialog import IconEditorDialog


class TestMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('图标编辑器测试')
        self.setGeometry(100, 100, 400, 300)
        
        # 创建测试图标数据
        self.test_icons = [
            WorldPatrolLargeMapIcon(
                icon_name="测试图标1",
                template_id="test_icon_1",
                lm_pos=[100, 200],
                tp_pos=[150, 250]
            ),
            WorldPatrolLargeMapIcon(
                icon_name="测试图标2", 
                template_id="test_icon_2",
                lm_pos=[300, 400],
                tp_pos=[350, 450]
            ),
            WorldPatrolLargeMapIcon(
                icon_name="测试图标3",
                template_id="test_icon_3", 
                lm_pos=[500, 600],
                tp_pos=None
            )
        ]
        
        # 创建UI
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        self.open_editor_btn = QPushButton('打开图标编辑器')
        self.open_editor_btn.clicked.connect(self.open_icon_editor)
        layout.addWidget(self.open_editor_btn)
        
        self.icon_editor_dialog = None
        
    def open_icon_editor(self):
        """打开图标编辑器"""
        if self.icon_editor_dialog is not None:
            self.icon_editor_dialog.show()
            self.icon_editor_dialog.activateWindow()
            return
            
        self.icon_editor_dialog = IconEditorDialog(self.test_icons, self)
        self.icon_editor_dialog.icon_selected.connect(self.on_icon_selected)
        self.icon_editor_dialog.icons_saved.connect(self.on_icons_saved)
        self.icon_editor_dialog.finished.connect(self.on_editor_closed)
        
        self.icon_editor_dialog.show()
        
    def on_icon_selected(self, icon_index: int):
        """处理图标选择"""
        print(f"选中图标索引: {icon_index}")
        
    def on_icons_saved(self, icon_list):
        """处理图标保存"""
        print(f"图标列表已更新，当前图标数量: {len(icon_list)}")
        for i, icon in enumerate(icon_list):
            print(f"  {i}: {icon.icon_name} ({icon.template_id})")
        self.test_icons = icon_list
        
    def on_editor_closed(self):
        """处理编辑器关闭"""
        self.icon_editor_dialog = None
        
    def get_current_calculated_pos(self):
        """模拟获取当前计算坐标"""
        return Point(123, 456)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestMainWindow()
    window.show()
    sys.exit(app.exec())
