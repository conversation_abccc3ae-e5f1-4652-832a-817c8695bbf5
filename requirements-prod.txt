#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --annotation-style=line --index-url=https://pypi.tuna.tsinghua.edu.cn/simple --output-file=requirements-prod.txt requirements-dev.txt
#

audioread==3.0.1          # via librosa
certifi==2024.7.4         # via requests
cffi==1.17.0              # via soundcard, soundfile
charset-normalizer==3.3.2  # via requests
coloredlogs==15.0.1       # via onnxruntime-directml
darkdetect==0.8.0         # via pyside6-fluent-widgets
decorator==5.1.1          # via librosa
flatbuffers==24.3.25      # via onnxruntime-directml
gensim==4.3.3             # via -r requirements-dev.txt
humanfriendly==10.0       # via coloredlogs
idna==3.7                 # via requests
joblib==1.4.2             # via librosa, scikit-learn
lazy-loader==0.4          # via librosa
librosa==0.10.2.post1     # via -r requirements-dev.txt
llvmlite==0.43.0          # via numba
mouseinfo==0.1.3          # via pyautogui
mpmath==1.3.0             # via sympy
msgpack==1.0.8            # via librosa
mss==9.0.1                # via -r requirements-dev.txt
numba==0.60.0             # via librosa
numpy==1.26.4             # via gensim, librosa, numba, onnxruntime-directml, opencv-python, scikit-learn, scipy, shapely, soundcard, soxr
onnxruntime-directml==1.18.0  # via -r requirements-dev.txt
opencv-python==*********  # via -r requirements-dev.txt
packaging==24.1           # via lazy-loader, onnxruntime-directml, pooch
pillow==10.4.0            # via pyscreeze
platformdirs==4.2.2       # via pooch
pooch==1.8.2              # via librosa
protobuf==3.20.2          # via onnxruntime-directml
pyautogui==0.9.54         # via -r requirements-dev.txt
pyclipper==1.3.0.post5    # via -r requirements-dev.txt
pycparser==2.22           # via cffi
pygetwindow==0.0.9        # via pyautogui
pymsgbox==1.0.9           # via pyautogui
pynput==1.7.7             # via -r requirements-dev.txt
pyperclip==1.9.0          # via mouseinfo
pyreadline3==3.4.1        # via humanfriendly
pyrect==0.2.0             # via pygetwindow
pyscreeze==0.1.30         # via pyautogui
pyside6==*******          # via -r requirements-dev.txt, pyside6-fluent-widgets
pyside6-addons==*******   # via pyside6
pyside6-essentials==*******  # via pyside6, pyside6-addons
pyside6-fluent-widgets==1.7.0  # via -r requirements-dev.txt
pysidesix-frameless-window==0.4.3  # via pyside6-fluent-widgets
pytweening==1.2.0         # via pyautogui
pywin32==306              # via pysidesix-frameless-window
pyyaml==6.0.1             # via -r requirements-dev.txt
requests==2.32.3          # via pooch
scikit-learn==1.5.1       # via librosa
scipy==1.13.1             # via gensim, librosa, scikit-learn
shapely==2.0.4            # via -r requirements-dev.txt
shiboken6==*******        # via pyside6, pyside6-addons, pyside6-essentials
six==1.16.0               # via pynput
smart-open==7.0.4         # via gensim
soundcard==0.4.3          # via -r requirements-dev.txt
soundfile==0.12.1         # via librosa
soxr==0.4.0               # via librosa
sympy==1.13.0             # via onnxruntime-directml
threadpoolctl==3.5.0      # via scikit-learn
typing-extensions==4.12.2  # via librosa
urllib3==2.2.2            # via requests
wrapt==1.16.0             # via smart-open
