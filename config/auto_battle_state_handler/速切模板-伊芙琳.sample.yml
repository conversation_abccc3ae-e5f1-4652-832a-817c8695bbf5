template_name: "速切模板-伊芙琳"
handlers:
  - states: "[前台-伊芙琳]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "伊芙琳-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "伊芙琳-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "伊芙琳-连携攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "设置状态"
                data: ["伊芙琳-缠绕禁制"]
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[伊芙琳-终结技可用]"
        operations:
          - operation_template: "伊芙琳-终结技"

      - states: "[伊芙琳-燎索点]{3, 3}"
        operations:
          - operation_template: "伊芙琳-长按A合轴连携技"

      - states: "[伊芙琳-特殊技可用]"
        sub_handlers:
          - states: "[伊芙琳-燎索点]{2, 2}"
            operations:
              - operation_template: "伊芙琳-长按E合轴连携技"

          - states: "[伊芙琳-燎火]{50, 99} & [伊芙琳-燎索点]{1, 1}"
            operations:
              - operation_template: "伊芙琳-长按E二连合轴连携技"

      - states: ""
        sub_handlers:
          - states: "[伊芙琳-燎火]{100, 100}"
            operations:
              - operation_template: "伊芙琳-长按A二连"

          - states: ""
            operations:
              - operation_template: "伊芙琳-普通攻击"