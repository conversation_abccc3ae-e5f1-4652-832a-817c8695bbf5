# 朱鸢站场的主要动作
main_zhu_yuan: &main  # 子弹不满的情况再打终结技
  - states: "[按键可用-终结技] & ![自定义-临时站场] & ![朱鸢-子弹数, 0, 999]{7, 9}"
    operations:
      - operation_template: "朱鸢-闪前A"
      - op_name: "等待秒数"
        data: ["0.3"]
      - op_name: "按键-终结技"
        post_delay: 4

  # 子弹不满的情况再打E
  - states: "[按键可用-特殊攻击] & ![自定义-朱鸢-特殊攻击] & ![朱鸢-子弹数, 0, 999]{7, 9}"
    sub_handlers:
      - states: "[自定义-朱鸢-下发普攻前滑]"
        operations:
          - op_name: "按键-普通攻击"
            post_delay: 0.8
          - op_name: "按键-特殊攻击"
            post_delay: 1.2
          - op_name: "设置状态"
            data: ["自定义-朱鸢-特殊攻击"]
      - states: ""
        operations:
          - operation_template: "朱鸢-闪前A"
          - op_name: "按键-特殊攻击"
            pre_delay: 0.8
            post_delay: 1.2
          - op_name: "设置状态"
            data: ["自定义-朱鸢-特殊攻击"]

  # 特殊攻击后 先长按清空弹匣
  - states: "[自定义-朱鸢-特殊攻击]"
    operations:
      - op_name: "按键-普通攻击"
        post_delay: 0.5
      - op_name: "按键-普通攻击"
        way: "按下"
        press: 1
      - operation_template: "朱鸢-蓄力3A"

  # 尽量多打3A
  - states: "[朱鸢-子弹数, 0, 999]{2, 9}"
    operations:
      - operation_template: "朱鸢-闪前A"
      - op_name: "按键-普通攻击"
        pre_delay: 0.3
        way: "按下"
        press: 0.5
      - operation_template: "朱鸢-蓄力3A"

  - states: "[自定义-朱鸢-下发普攻前滑]"
    operations:
      # 先闪A打断当前动作
      - operation_template: "朱鸢-后4A"
      - op_name: "设置状态"
        data: ["自定义-朱鸢-下发普攻前滑"]

  - states: ""
    operations:
      # 先闪A打断当前动作
      - operation_template: "朱鸢-闪前A"
      - op_name: "等待秒数"
        data: ["0.6"]
      - operation_template: "朱鸢-5A"
      - op_name: "设置状态"
        data: ["自定义-朱鸢-下发普攻前滑"]

handlers:
  - states: "[前台-朱鸢]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 2]"
        sub_handlers:
          - state_template: "支援攻击模板-全角色"
      - states: "[自定义-连携换人, 0, 1]"
        operations:
          - operation_template: "通用-连携攻击"
      - states: "[自定义-连携换人, 0, 1]"
        operations:
          - operation_template: "通用-连携攻击"

      # 闪避后
      - states: "[自定义-红光闪避]"
        operations:
          - op_name: "按键-普通攻击"
            pre_delay: 0.8
            post_delay: 1
          - op_name: "按键-普通攻击"
            post_delay: 1
            repeat: 2
          - op_name: "清除状态"
            state: "自定义-红光闪避"

      - states: "[自定义-快速支援换人]"
        operations:
          - op_name: "等待秒数"
            data: ["0.5"]
          - op_name: "清除状态"
            data: ["自定义-快速支援换人"]
          - op_name: "设置状态"
            state: "自定义-临时站场"

      - states: "[自定义-血量扣减]"
        operations:
          - op_name: "按键-闪避"
            pre_delay: 0.5
            post_delay: 0.5
          - op_name: "清除状态"
            state: "自定义-血量扣减"

      - states: ""
        sub_handlers: *main