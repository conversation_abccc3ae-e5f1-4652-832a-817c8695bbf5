handlers:
  - states: "(![按键-切换角色-下一个, 0, 0.3]& ![按键-切换角色-上一个, 0, 0.3])"
    debug_name: "未知角色"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "通用-支援攻击"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "通用-连携攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3]) | [自定义-快速支援换人, 0, 1]"
        operations:
          - op_name: "等待秒数"
            seconds: 0.1

      - states: "[按键可用-终结技]"
        operations:
          - op_name: "设置状态"
            state: "自定义-动作不打断"
            seconds: 5
          - op_name: "按键-终结技"
            post_delay: 2.5

      - states: "[按键可用-特殊攻击]"
        operations:
          - op_name: "设置状态"
            state: "自定义-动作不打断"
            seconds: 1
          - op_name: "按键-特殊攻击"
            post_delay: 0.5
            repeat: 2

      - states: ""
        operations:
          - operation_template: "通用-切人普通攻击"