template_name: "速切模板-可琳"
handlers:
  - states: "[前台-可琳]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "可琳-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "通用-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "可琳-连携攻击"

      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - operation_template: "可琳-快速支援"
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[可琳-终结技可用] & [自定义-连携换人, 0, 10]"
        operations:
          - op_name: "按键-终结技"
            post_delay: 0.1
            repeat: 10
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 30

      # 可以用特殊技就持续按
      - states: "[可琳-特殊技可用]"
        sub_handlers:
          - states: "[自定义-连携换人, 0, 10] & ![自定义-可琳-这次不打特殊技, 0, 12]"
            debug_name: "本次失衡已经打过特殊技"
            post_delay: 0.5
            operations:
              - op_name: "设置状态"
                state: "自定义-可琳-这次不打特殊技"
              - operation_template: "可琳-强化特殊技"
          - states: "[自定义-妮可-能量场, -5, 0]"
            operations:
              - operation_template: "可琳-强化特殊技"
      # 没有特殊技可以用
      - states: ""
        operations:
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 35