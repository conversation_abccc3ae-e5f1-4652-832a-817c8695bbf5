handlers:
  - states: "[前台-柳]"
    sub_handlers:
      - states: "[按键-切换角色-下一个, 0, 0.4]"
        operations:
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 4
      - states: "[按键可用-特殊攻击]"
        operations:
          - op_name: "设置状态"
            state: "自定义-动作不打断"
          - op_name: "按键-特殊攻击"
            way: "按下"
            post_delay: 0.7
          - op_name: "按键-特殊攻击"
            way: "松开"
          - op_name: "设置状态"
            state: "自定义-柳-森罗万象"
          - op_name: "清除状态"
            state: "自定义-动作不打断"
      - states: "[按键可用-终结技] & ![自定义-动作不打断, 0, 15]"
        operations:
          - op_name: "设置状态"
            state: "自定义-动作不打断"
          - op_name: "按键-终结技"
            post_delay: 1
            repeat: 5
          - op_name: "清除状态"
            state: "自定义-动作不打断"
        # 普攻
      - states: "[自定义-柳-森罗万象, 0, 15]"
        operations:
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 20
      - states: ""
        operations:
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 35