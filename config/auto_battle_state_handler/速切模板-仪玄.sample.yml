template_name: "速切模板-仪玄"
handlers:
  - states: "[前台-仪玄]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "仪玄-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        sub_handlers:
        - states: "[仪玄-术法值]{0, 119} & [仪玄-能量]{60, 120}"
          sub_handlers:
            - states: "[前台-血量扣减]{1, 100}"
              debug_name: "被人胖揍过开格挡"
              operations:
                - operation_template: "仪玄-强化特殊技三连"
                
        - states: ""
          operations:
            - operation_template: "仪玄-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "仪玄-连携攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - operation_template: "通用-快速支援"
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[自定义-连携换人, 0, 10]"
        sub_handlers:
          - states: "[仪玄-终结技可用] & [仪玄-术法值]{120, 120}"
            debug_name: "法术终结技"
            operations:
              - operation_template: "仪玄-法术终结技"

          - states: "[仪玄-终结技可用]"
            debug_name: "喧嚣终结技"
            operations:
              - operation_template: "仪玄-喧嚣终结技"

          - states: "[仪玄-特殊技可用]"
            operations:
              - operation_template: "仪玄-凸"

          - states: "![仪玄-特殊技可用]"
            operations:
              - op_name: "设置状态"
                data: ["自定义-仪玄-大太极"]
              - operation_template: "仪玄-太极合轴"

      - states: "[自定义-仪玄-大太极,0,1] | [自定义-仪玄-小太极,0,2]"
        debug_name: "太极中，等待一下"
        operations:
          - op_name: "等待秒数"
            seconds: 1

      - states: "([仪玄-术法值]{0, 119} & [仪玄-能量]{60, 120}) | [仪玄-能量]{110, 120}"
        sub_handlers:
          - states: "[前台-血量扣减]{1, 100}"
            debug_name: "被人胖揍过开格挡"
            operations:
              - operation_template: "仪玄-强化特殊技三连"
          - states: ""
            debug_name: "割草长按A"
            operations:
              - operation_template: "仪玄-凸"

      - states: "[仪玄-玄墨值]{1, 1}"
        operations:
          - op_name: "设置状态"
            data: ["自定义-仪玄-大太极"]
          - operation_template: "仪玄-太极合轴"

      - states: "[后台-1-潘引壶] & [潘引壶-特殊技可用]"
        debug_name: "让熊补buff吧"
        operations:
          - op_name: "设置状态"
            data: ["自定义-仪玄-小太极"]
          - operation_template: "仪玄-太极合轴"

      - states: "([后台-1-击破] | [后台-2-击破]) & [仪玄-术法值]{120, 120}"
        debug_name: "有力量了，上来打击破吧"
        operations:
          - op_name: "设置状态"
            data: ["自定义-仪玄-小太极"]
          - operation_template: "仪玄-太极合轴"

      - states: "[仪玄-终结技可用] & [仪玄-术法值]{120, 120}"
        debug_name: "法术终结技"
        operations:
          - operation_template: "仪玄-法术终结技"

      - states: "[仪玄-终结技可用]"
        debug_name: "喧嚣终结技"
        operations:
          - operation_template: "仪玄-喧嚣终结技"

      - states: ""
        debug_name: "没能量了"
        operations:
          - operation_template: "仪玄-普通攻击"
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
