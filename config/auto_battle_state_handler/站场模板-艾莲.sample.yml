handlers:
  - states: "[前台-艾莲]"
    sub_handlers:
      # 大哥在场你怎么好意思站场的？
      - states: "[自定义-黄光切人, 0, 1.5]"
        operations:
          - operation_template: "艾莲-支援攻击"
      - states: "[自定义-连携换人, 0, 1]"
        operations:
          - operation_template: "艾莲-连携攻击"

      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3]) | [自定义-快速支援换人, 0, 1]"
        operations:
          - op_name: "清除状态"
            state: "自定义-动作不打断"

      # 失衡时刻
      - states: "([自定义-连携换人, 0, 10]"
        sub_handlers:
          # 失衡阶段：Q
          - states: "[按键可用-终结技] "
            operations:
              - operation_template: "艾莲-终结技"

          # 失衡阶段 EA 2连
          - states: "[自定义-连携换人, 0, 10] & [艾莲-能量]{40, 120} & [按键可用-特殊攻击]"
            operations:
              - op_name: "按键-闪避"
                post_delay: 0.1
              - op_name: "按键-普通攻击"
                post_delay: 0.1
              - operation_template: "艾莲-EA连击"

      # 失衡阶段：Q 后来没击破就直接放
      - states: "[自定义-艾莲-刚踢完剪刀, 0, 2] & ![后台-1-击破] & ![后台-2-击破] & [按键可用-终结技] "
        operations:
          - operation_template: "艾莲-终结技"

      # 失衡阶段 EA 2连
      - states: "[自定义-连携换人, 0, 10] & [艾莲-能量]{40, 120} & [按键可用-特殊攻击]"
        operations:
          - op_name: "按键-闪避"
            post_delay: 0.1
          - op_name: "按键-普通攻击"
            post_delay: 0.1
          - operation_template: "艾莲-EA连击"

      # 有击破队友在后面就别乱用能量了
      - states: "[艾莲-急冻充能]{0, 4} & [艾莲-能量]{80, 120} & [按键可用-特殊攻击]  & ![后台-1-击破] & ![后台-2-击破]"
        operations:
          - operation_template: "艾莲-EA连击"
          - op_name: "设置状态"
            state: "自定义-艾莲-刚踢完剪刀"

      # 能量要满了
      - states: "[艾莲-急冻充能]{0, 4} & [艾莲-能量]{110, 120}"
        operations:
          - operation_template: "艾莲-EA连击"
          - op_name: "设置状态"
            state: "自定义-艾莲-刚踢完剪刀"

      - states: "![艾莲-急冻充能]{3, 6}"
        sub_handlers:
          - states: "[自定义-艾莲-蓄力加速, 0, 12]"
            operations:
              - operation_template: "艾莲-冲刺蓄力攻击-慢"
              - op_name: "清除状态"
                state: "自定义-艾莲-蓄力加速"
              - operation_template: "艾莲-普通攻击"
          - states: ""
            operations:
              - operation_template: "艾莲-冲刺蓄力攻击"
              - operation_template: "艾莲-普通攻击"

      - states: "[艾莲-急冻充能]{3, 6}"
        operations:
          - operation_template: "艾莲-普通攻击"
          - op_name: "设置状态"
            state: "自定义-艾莲-刚踢完剪刀"