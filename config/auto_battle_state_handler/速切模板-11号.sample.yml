template_name: "速切模板-11号"
handlers:
  - states: "[前台-11号]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "通用-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "通用-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "通用-连携攻击"
          - op_name: "设置状态"
            state: "自定义-11号-火力压制"

      # 初始化入场状态
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "清除状态"
                state: "自定义-11号-火力压制"
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "清除状态"
                state: "自定义-11号-火力压制"
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[11号-终结技可用] & ![自定义-11号-火力压制, 0, 6] & ![自定义-连携换人, 0, 6]"
        operations:
          - op_name: "设置状态"
            state: "自定义-11号-火力压制"
          - operation_template: "通用-终结技"
          - operation_template: "11号-火刀八连"
          - op_name: "清除状态"
            state: "自定义-11号-火力压制"

      - states: "[自定义-连携换人, 4, 10]"
        operations:
          - op_name: "设置状态"
            state: "自定义-11号-火力压制"
          - op_name: "按键-闪避"
            post_delay: 0.2
          - op_name: "按键-特殊攻击"
            post_delay: 1.2
          - operation_template: "11号-火刀八连"
          - op_name: "清除状态"
            state: "自定义-11号-火力压制"

      - states: "[11号-能量]{80, 120} & ![自定义-11号-火力压制, 0, 6] & ![自定义-连携换人, 0, 6]"
        operations:
          - op_name: "设置状态"
            state: "自定义-11号-火力压制"
          - op_name: "按键-闪避"
            post_delay: 0.2
          - op_name: "按键-特殊攻击"
            post_delay: 1.2
          - operation_template: "11号-火刀八连"
          - op_name: "清除状态"
            state: "自定义-11号-火力压制"

      - states: "![自定义-11号-火力压制, 0, 6] & ![11号-能量]{80, 120}"
        operations:
          - operation_template: "11号-火刀八连"
          - op_name: "设置状态"
            data: ["自定义-速切结束"]