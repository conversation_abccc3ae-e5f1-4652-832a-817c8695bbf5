# 作者 火
template_name: "速切模板-莱特"
handlers:
  - states: "[前台-莱特]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        sub_handlers:
          - state_template: "支援攻击模板-全角色"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "通用-闪A"

      # 优先把士气变成BUFF
      # 因为不好估算层数，就以士气值作为结束条件，浪费一点也无所谓
      - states: "[自定义-莱特-欧拉欧拉, 0, 30]"
        sub_handlers:
          - states: "[莱特-士气]{0, 70}"
            operations:
              - op_name: "清除状态"
                state_list: ["自定义-莱特-欧拉欧拉"]
              - op_name: "设置状态"
                state: "自定义-莱特-BUFF"
              # 加完BUFF，可以再放个Q，不浪费时间
          - states: ""
            operations:
              - op_name: "按键-普通攻击"
                post_delay: 0.1

      # 刚切换角色的前置处理
      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[按键可用-连携技, 0, 0.5]"
        sub_handlers:
          - states: "![自定义-莱特-BUFF, 0, 16] & [莱特-士气]{80, 100}"
            operations:
              - op_name: "设置状态"
                state_list: ["自定义-莱特-欧拉欧拉"]
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 3
              - op_name: "设置状态"
                data: ["自定义-速切结束"]

      - states: "[自定义-闪避]"
        sub_handlers:
          - states: "![自定义-莱特-BUFF, 0, 16] & [莱特-士气]{80, 100}"
            operations:
              - op_name: "设置状态"
                state_list: ["自定义-莱特-欧拉欧拉"]
          - states: ""
            operations:
              - op_name: "按键-普通攻击"
                post_delay: 0.2
                repeat: 8
              - op_name: "等待秒数"
                seconds: 0.5
              - op_name: "设置状态"
                data: ["自定义-速切结束"]

      - states: "[自定义-快速支援换人]"
        sub_handlers:
          - states: "![自定义-莱特-BUFF, 0, 16] & [莱特-士气]{80, 100}"
            operations:
              - op_name: "设置状态"
                state_list: ["自定义-莱特-欧拉欧拉"]
          - states: ""
            # 其实这里会在士气够的情况下刷新BUFF，不过感觉不好判断，先不管了
            operations:
              - op_name: "按键-普通攻击"
                post_delay: 0.2
                repeat: 8
              - op_name: "等待秒数"
                seconds: 0.5
              - op_name: "设置状态"
                data: ["自定义-速切结束"]

      - states: "[自定义-招架支援]"
        sub_handlers:
          - states: "![自定义-莱特-BUFF, 0, 16] & [莱特-士气]{80, 100}"
            operations:
              - op_name: "设置状态"
                state_list: ["自定义-莱特-欧拉欧拉"]
          - states: ""
            operations:
              # 其实这里会在士气够的情况下刷新BUFF，不过感觉不好判断，先不管了
              - op_name: "按键-普通攻击"
                post_delay: 0.2
                repeat: 8
              - op_name: "等待秒数"
                seconds: 0.5
              - op_name: "设置状态"
                data: ["自定义-速切结束"]

      # 士气够了、BUFF快结束了，才进入到放BUFF的状态
      - states: "![自定义-莱特-BUFF, 0, 16] & [莱特-士气]{80, 100}"
        operations:
          - op_name: "设置状态"
            state_list: ["自定义-莱特-欧拉欧拉"]

      # Q技能
      - states: "[莱特-终结技可用]"
        operations:
          - op_name: "设置状态"
            state: "自定义-动作不打断"
            seconds: 2
          - op_name: "按键-终结技"
            post_delay: 0.5
            repeat: 4
          - op_name: "清除状态"
            state: "自定义-动作不打断"
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
      # E技能
      - states: "[莱特-能量]{80, 120} & [莱特-特殊技可用]"
        operations:
          - op_name: "设置状态"
            state: "自定义-动作不打断"
            seconds: 1.6
          - op_name: "按键-特殊攻击"
            post_delay: 0.4
            repeat: 4
          - op_name: "清除状态"
            state: "自定义-动作不打断"
          - op_name: "设置状态"
            data: ["自定义-速切结束"]

      # 加不了BUFF也没有技能，不普通攻击，直接下场
      - states: ""
        operations:
          - operation_template: "通用-切人普通攻击"