template_name: "速切模板-格莉丝"
handlers:
  - states: "[前台-格莉丝]"
    sub_handlers:
      - states: "[自定义-异常-冰]{0, 99} | [自定义-异常-物理]{0, 99} | [自定义-异常-烈霜]{0, 99} | [自定义-异常-火]{0, 99}"
        debug_name: "清除其他异常积蓄"
        operations:
          - op_name: "清除状态"
            state_list:
              [
                "自定义-异常-火",
                "自定义-异常-冰",
                "自定义-异常-烈霜",
                "自定义-异常-物理",
              ]

      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "格莉丝-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "通用-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "格莉丝-连携攻击"

      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[格莉丝-终结技可用]"
        operations:
          - operation_template: "格莉丝-终结技"

        # 格莉丝3段a1e
      - states: ""
        operations:
          - operation_template: "格莉丝-3AEAE"
          - operation_template: "格莉丝-3AEAE"
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
