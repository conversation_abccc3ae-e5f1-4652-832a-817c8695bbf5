template_name: "速切模板-本"
handlers:
  - states: "[前台-本]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "本-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "通用-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "通用-连携攻击"

      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[本-终结技可用] & [自定义-血量扣减, 0, 2] "
        operations:
          - op_name: "设置状态"
            data: ["自定义-本-守卫"]
          - operation_template: "通用-终结技"
          - op_name: "清除状态"
            state_list: ["自定义-血量扣减"]

      - states: "[本-特殊技可用]"
        operations:
          - op_name: "设置状态"
            state: "自定义-本-守卫"
          - op_name: "等待秒数"
            seconds: 0.05
          - op_name: "按键-特殊攻击"
            post_delay: 0.1
            repeat: 5
          - op_name: "设置状态"
            data: ["自定义-速切结束"]

      - states: ""
        operations:
          - operation_template: "通用-切人普通攻击"