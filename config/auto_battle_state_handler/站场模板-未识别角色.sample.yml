# 开场未识别到角色时使用 默认什么都不做 避免影响正常连招
handlers:
  - states: "![前台-击破] & ![前台-强攻] & ![前台-支援] & ![前台-防护] & ![前台-异常]& ![前台-命破]"
    debug_name: "角色识别中，检查皮肤设置"
    operations:
      - op_name: "按键-特殊攻击"
        way: "松开"
      - op_name: "按键-移动-前"
        way: "松开"
      - op_name: "按键-移动-左"
        way: "松开"
      - op_name: "按键-移动-后"
        way: "松开"
      - op_name: "按键-移动-右"
        way: "松开"
      - op_name: "等待秒数"
        seconds: 0.1