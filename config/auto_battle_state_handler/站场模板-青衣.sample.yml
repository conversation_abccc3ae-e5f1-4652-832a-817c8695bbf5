handlers:
  - states: "[前台-青衣]"
    sub_handlers:
      - states: "[青衣-电压]{80, 101}"
        operations:
          - op_name: "设置状态"
            state: "自定义-青衣-醉花云月转"
          - op_name: "按键-普通攻击"
            way: "按下"
            press: 2.5
            post_delay: 0.5
          - op_name: "设置状态"
            state: "自定义-青衣-特殊技续普攻"
          - op_name: "清除状态"
            state: "自定义-青衣-普攻次数"

      - states: "[按键可用-终结技] & ![自定义-动作不打断, 0, 30] & ![自定义-青衣-醉花云月转, 0, 3]"
        operations:
          - op_name: "设置状态"
            state: "自定义-动作不打断"
          - op_name: "按键-终结技"
            post_delay: 2
            repeat: 2
          - op_name: "清除状态"
            state: "自定义-动作不打断"

      - states: "![按键可用-特殊攻击] & [自定义-青衣-特殊技续普攻, 0, 2]"
        operations:
          - operation_template: "通用-闪避-前"
          - op_name: "清除状态"
            state: "自定义-青衣-特殊技续普攻"
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 10
          - op_name: "按键-普通攻击"
            post_delay: 0.5

      - states: "[按键可用-特殊攻击] & [自定义-青衣-特殊技续普攻, 0, 2]"
        operations:
          - op_name: "设置状态"
            state: "自定义-动作不打断"
          - op_name: "按键-特殊攻击"
            post_delay: 0.5
            repeat: 4
          - op_name: "清除状态"
            state: "自定义-动作不打断"

      - states: "[青衣-电压]{0, 90}"
        operations:
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 20
          - op_name: "按键-普通攻击"
            post_delay: 0.5
          - op_name: "设置状态"
            state: "自定义-青衣-特殊技续普攻"

      - states: ""
        operations:
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 10