# 闪避普攻 普攻后摇等待时间各角色不同 由后续动作控制
handlers:
  - states: "![自定义-快速支援换人, 0, 0.5] & ![自定义-不使用快速支援, -10, 0]"
    debug_name: "快速支援可用"
    sub_handlers:
      # 终结技快速支援变连携
      - states: "[自定义-耀嘉音-终结技连携, 0, 99]{1, 2}"
        operations:
          - op_name: "按键-快速支援"
          - op_name: "设置状态"
            state: "自定义-耀嘉音-终结技连携"
            add: -1
          - op_name: "设置状态"
            state: "按键可用-连携技"
          - op_name: "清除状态"
            state_list: ["自定义-速切结束", "自定义-动作不打断"]
          - op_name: "等待秒数"
            seconds: 0.1

      - states: "[前台-耀嘉音] & ![耀嘉音-终结技可用]"
        operations:
          - op_name: "设置状态"
            state: "自定义-快速支援换人"
          - op_name: "设置状态"
            state: "自定义-耀嘉音-唱歌"
          - op_name: "按键-快速支援"
          - op_name: "清除状态"
            state_list: ["自定义-速切结束", "自定义-动作不打断"]
          - op_name: "等待秒数"
            seconds: 0.1
      # 耀嘉音有终结技不切人, 有耀嘉音不切人, 除非耀嘉音或者其他支援防护在前台

      - states: "((![耀嘉音-能量]{0, 120} | [前台-耀嘉音]) & ![耀嘉音-终结技可用])"
        debug_name: "正常快速支援"
        operations: &switch_to_next
          - op_name: "设置状态"
            state: "自定义-快速支援换人"
          - op_name: "按键-快速支援"
          - op_name: "清除状态"
            state_list: ["自定义-速切结束", "自定义-动作不打断"]
          - op_name: "等待秒数"
            seconds: 0.1
      - states: "[前台-支援]"
        operations: *switch_to_next
      - states: "[前台-防护]"
        operations: *switch_to_next
      - states: "[前台-波可娜]"
        operations: *switch_to_next
