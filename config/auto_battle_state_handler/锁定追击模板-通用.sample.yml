template_name: "锁定追击模板-通用"
handlers:
  - states: "![锁定靶心-近距离,0,5]"
    sub_handlers:
      # 因为有些角色距离远了也无所谓，所以这里不做处理
      - states: "![锁定靶心-远距离,0,5]"
        debug_name: "丢失锁定"
        operations:
          - op_name: "按键-锁定敌人"
            way: "按下"
            press: 0.05
          - operation_template: "通用-闪避-前"
          - op_name: "按键-普通攻击"
            post_delay: 0.1
          - op_name: "等待秒数"
            seconds: 0.2
