template_name: "速切模板-雨果"
handlers:
  - states: "[前台-雨果]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "雨果-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "雨果-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "雨果-连携攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - operation_template: "雨果-快速支援"
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[自定义-连携换人, 0, 10]"
        sub_handlers:
          - states: "[雨果-终结技可用]"
            operations:
              - operation_template: "雨果-终结技"

          - states: "[雨果-特殊技可用]"
            operations:
              - operation_template: "雨果-强化特殊攻击"

      - states: "[后台-1-击破] | [后台-2-击破]"
        debug_name: "无能量普攻"
        operations:
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
          - operation_template: "雨果-普通攻击"

      - states: ""
        debug_name: "无能量普攻"
        operations:
          - operation_template: "雨果-普通攻击"
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
