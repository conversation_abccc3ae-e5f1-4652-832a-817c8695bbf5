handlers:
  - states: "[前台-简]"
    sub_handlers:
      - states: "![简-狂热心流]{60, 101} & [简-萨霍夫跳]"
        operations:
          - op_name: "按键-闪避"
            post_delay: 0.1
          - op_name: "设置状态"
            state: "自定义-动作不打断"
            value: 1
          - op_name: "按键-普通攻击"
            post_delay: 0.2
          - op_name: "按键-普通攻击"
            way: "按下"
            post_delay: 0.9
          - op_name: "按键-特殊攻击"
            post_delay: 0.1
            repeat: 18
          - op_name: "清除状态"
            state: "自定义-动作不打断"
      - states: "[按键可用-终结技]"
        operations:
          - op_name: "按键-终结技"
            post_delay: 0.2
            repeat: 2
      - states: "[按键可用-特殊攻击]"
        operations:
          - op_name: "按键-特殊攻击"
            press: 0.5
            repeat: 4
      - states: ""
        operations:
          - op_name: "按键-普通攻击"
            post_delay: 0.2