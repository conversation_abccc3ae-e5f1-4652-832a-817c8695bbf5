# 连携后摇等待时间各角色不同 由后续动作控制
handlers:
  # 连携条件
  - states: "[连携技-1-邦布]"
    sub_handlers:
      - states: "[连携技-2-强攻]"
        operations: &link_ops
          - op_name: "按键-连携技-左"
            post_delay: 1
          - op_name: "设置状态"
            state: "自定义-连携换人"
            seconds: 1
          - op_name: "设置状态"
            state: "自定义-动作不打断"
            seconds: 1
      - states: "[连携技-2-异常]"
        operations: *link_ops
