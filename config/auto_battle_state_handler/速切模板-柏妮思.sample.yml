template_name: "速切模板-柏妮思"
handlers:
  - states: "[前台-柏妮思]"
    sub_handlers:
      - states: "[自定义-异常-物理, 0, 99] | [自定义-异常-冰, 0, 99] | [自定义-异常-烈霜, 0, 99] | [自定义-异常-电, 0, 99]"
        debug_name: "清除其他异常积蓄"
        operations:
          - op_name: "清除状态"
            state_list:
              [
                "自定义-异常-冰",
                "自定义-异常-物理",
                "自定义-异常-烈霜",
                "自定义-异常-电"
              ]

      - states: "[自定义-黄光切人, 0, 1]"
        debug_name: "黄光切人"
        operations:
          - operation_template: "柏妮思-格挡攻击"
          - operation_template: "柏妮思-短按单双喷"

      - states: "[自定义-红光闪避, 0, 1]"
        debug_name: "红光闪避"
        operations:
          - operation_template: "柏妮思-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        debug_name: "连携攻击"
        operations:
          - operation_template: "柏妮思-连携攻击"
          - operation_template: "柏妮思-短按单双喷"

      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            debug_name: "快速支援等待"
            operations:
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            debug_name: "短暂等待"
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[柏妮思-终结技可用]"
        debug_name: "终结技"
        operations:
          - operation_template: "柏妮思-终结技"

      - states: "[柏妮思-燃点]{100, 100}"
        debug_name: "满燃点上灼烧"
        operations:
          - operation_template: "柏妮思-长按普通攻击"
            data: ["自定义-速切结束"]

      - states: "[柏妮思-燃点]{60, 100}"
        debug_name: "正常打站场"
        operations:
          - operation_template: "柏妮思-长长按普通攻击"

      - states: "[柏妮思-特殊技可用]"
        debug_name: "低燃点单喷接双喷"
        operations:
          - operation_template: "柏妮思-短按单双喷"

      - states: "[后台-1-异常] | [后台-2-异常]"
        debug_name: "充能打普攻"
        operations:
          - operation_template: "柏妮思-普通攻击"
          - op_name: "设置状态"
            data: ["自定义-速切结束"]

      - states: ""
        debug_name: "单异常，马上离开"
        operations:
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
          - operation_template: "柏妮思-普通攻击"