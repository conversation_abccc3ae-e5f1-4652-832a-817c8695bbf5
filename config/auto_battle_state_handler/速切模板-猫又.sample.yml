template_name: "速切模板-猫又"
handlers:
  - states: "[前台-猫又]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "猫又-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "通用-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "猫又-连携攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "等待秒数"
                seconds: 0.5
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3
      # 失衡时刻
      - states: "[自定义-连携换人, 0, 10]"
        sub_handlers:
          # 失衡的时候就不用考虑这么多了，射吧
          - states: "[猫又-终结技可用]"
            operations:
              - operation_template: "猫又-终结技"
          # 朴实无华
          - states: "[猫又-特殊技可用]"
            operations:
              - op_name: "设置状态"
                state: "自定义-动作不打断"
                seconds: 0.6
              - op_name: "按键-特殊攻击"
                post_delay: 0.3
                repeat: 2

          - states: ""
            operations:
              # 重击 接三段普攻, 不打满尽量背身E
              - op_name: "按键-普通攻击"
                post_delay: 0.1
                repeat: 25

      - states: "[猫又-终结技可用] & !([按键-切换角色-下一个, 0, 1]|[按键-切换角色-上一个, 0, 1]) & ![自定义-动作不打断, 0, 30]"
        operations:
          - op_name: "设置状态"
            state: "自定义-动作不打断"
            seconds: 2
          - op_name: "按键-终结技"
            post_delay: 1
            repeat: 2

      - states: "[猫又-特殊技可用]"
        operations:
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 25
          - op_name: "设置状态"
            state: "自定义-动作不打断"
            seconds: 2
          - op_name: "按键-特殊攻击"
            post_delay: 0.2
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
          - op_name: "按键-特殊攻击"
            post_delay: 0.8

      - states: ""
        operations:
          # 重击 接三段普攻, 不打满尽量背身E
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 50
          - op_name: "设置状态"
            data: ["自定义-速切结束"]