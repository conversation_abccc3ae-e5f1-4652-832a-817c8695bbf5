template_name: "速切模板-派派"
handlers:
  - states: "[前台-派派]"
    sub_handlers:
      - states: "[自定义-异常-火, 0, 99] | [自定义-异常-冰, 0, 99] | [自定义-异常-烈霜, 0, 99] | [自定义-异常-电, 0, 99]"
        debug_name: "清除其他异常积蓄"
        operations:
          - op_name: "清除状态"
            state_list:
              [
                "自定义-异常-火",
                "自定义-异常-冰",
                "自定义-异常-烈霜",
                "自定义-异常-电",
              ]

      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "派派-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "派派-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "派派-连携攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[派派-终结技可用]"
        debug_name: "终结技坐~稳~啦~"
        operations:
          - operation_template: "派派-终结技"

      # 派派不得站场超过6秒
      - states: "!([按键-切换角色-下一个, 0, 6]|[按键-切换角色-上一个, 0, 6])"
        debug_name: "站场时间不足6秒"
        sub_handlers:
          - states: "[派派-特殊技可用]"
            debug_name: "超级帅的引擎转下砸合轴"
            operations:
              - operation_template: "派派-3A速启强化特殊技合轴"
          - states: ""
            debug_name: "超级帅的轮胎转下砸合轴"
            operations:
              - operation_template: "派派-3A速启特殊技合轴"

      - states: "[派派-特殊技可用]"
        debug_name: "非常重的引擎转"
        operations:
          - operation_template: "派派-3A速启强化特殊技"

      - states: ""
        debug_name: "有亿点重的轮胎转"
        operations:
          - operation_template: "派派-3A速启特殊技"
