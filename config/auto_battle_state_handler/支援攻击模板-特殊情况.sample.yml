# 需要特别处理的模板
handlers:
  - states: "![前台-薇薇安] & [薇薇安-能量]{0, 120}"
    operations:
      - op_name: "按键-切换角色"
        agent_name: "薇薇安"

  - states: "![前台-浮波柚叶] & [浮波柚叶-能量]{0, 120}"
    operations:
      - op_name: "按键-切换角色"
        agent_name: "浮波柚叶"

  # 青衣特殊技不可用的情况下没必要切人，直接闪A
  - states: "![自定义-血量扣减, 0, 8]"
    sub_handlers:
      - states: "[前台-青衣]"
        sub_handlers:
          - state_template: "闪A模板-青衣"
      - states: "[前台-零号安比]"
        sub_handlers:
          - state_template: "双反模板-下一个"
  - states: "[前台-异常] & ![自定义-动作不打断, -10, -0.1]"
    sub_handlers:
      - states: "([按键-切换角色-下一个, 0, 6]|[按键-切换角色-上一个, 0, 6])" # 异常角色至少输出6秒
        sub_handlers:
          - state_template: "闪A模板-全角色" # 强者不需要切人直接格挡就行
  - states: "[前台-凯撒]"
    operations:
      - op_name: "设置状态"
        data: ["自定义-凯撒-护盾"]
      - operation_template: "凯撒-特殊技格挡"
      - op_name: "设置状态"
        data: ["自定义-速切结束"] # 强者不需要切人直接格挡就行
  - states: "[前台-本]"
    operations:
      - op_name: "设置状态"
        state: "自定义-本-守卫"
      - op_name: "按键-特殊攻击"
        post_delay: 0.1
        repeat: 5
      - op_name: "设置状态"
        data: ["自定义-速切结束"]
