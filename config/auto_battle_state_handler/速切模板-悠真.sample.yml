template_name: "速切模板-悠真"
handlers:
  - states: "[前台-悠真]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "通用-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "通用-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "悠真-连携攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      # 失衡时刻
      - states: "[自定义-连携换人, 0, 10]"
        sub_handlers:
          # 离场
          - states: "[自定义-连携换人, 9, 10]"
            debug_name: "失衡快结束"
            operations:
              - operation_template: "悠真-连携普攻产电壶"  # 连携奖励电壶别浪费了
              - op_name: "设置状态"
                data: ["自定义-速切结束"]

          # 连邂后第一套技能
          - states: "[自定义-连携换人, 0, 3] | [自定义-连携跳过, 0, 2]"  # 清空掉异常打出的电壶
            debug_name: "失衡期操作"
            sub_handlers:
              - states: "[悠真-终结技可用]"
                operations:
                  - operation_template: "悠真-终结技"  # 上一轮的电壶
                  - operation_template: "悠真-射箭四刀"  # 清空连携电壶
                  - operation_template: "悠真-连携普攻产电壶"  # 连携奖励电壶别浪费了
                  - operation_template: "悠真-射箭四刀"  # 清空连携电壶

              - states: "![悠真-终结技可用]"
                operations:
                  - operation_template: "悠真-射箭四刀"
                  - operation_template: "悠真-连携普攻产电壶"  # 连携奖励电壶别浪费了
                  - operation_template: "悠真-射箭四刀"  # 清空连携电壶

          - states: "[自定义-连携换人, 3, 10]"
            debug_name: "失衡期"
            sub_handlers:
              # 妮可来一发谢谢
              - states: "([妮可-特殊技可用] | [妮可-终结技可用]) & [悠真-特殊技可用] &![自定义-妮可-能量场, -15, 3.5]"
                debug_name: "妮可能量场已过"
                operations:
                  - operation_template: "悠真-强化特殊技合轴"  # 清空上一轮电壶

              # 从妮可那回来了
              - states: "[自定义-快速支援换人, 0, 2]"
                sub_handlers:
                  - states: "[悠真-终结技可用]"
                    operations:
                      - operation_template: "悠真-终结技"
                      - operation_template: "悠真-射箭四刀"  # 清空连携电壶
                  - states: "![悠真-终结技可用]"
                    operations:
                      - operation_template: "悠真-射箭四刀"  # 清空连携电壶

              - states: "[悠真-终结技可用]"
                sub_handlers:
                  - states: "[悠真-特殊技可用]"
                    operations:
                      - operation_template: "悠真-强化特殊技"
                      - operation_template: "悠真-终结技"
                      - operation_template: "悠真-射箭四刀"  # 清空连携电壶

              - states: "[悠真-特殊技可用]"
                operations:
                  - operation_template: "悠真-强化特殊技速接普攻"

              - states: "![悠真-特殊技可用]"
                operations:
                  - operation_template: "悠真-连携普攻产电壶"  # 连携奖励电壶别浪费了
                  - op_name: "设置状态"
                    data: ["自定义-速切结束"]

      # 非失衡时刻

      # 后台如果没有击破
      - states: "![后台-1-击破] & ![后台-2-击破]"
        debug_name: "非击破队"
        sub_handlers:
          # 异常队，无脑输出
          - states: "[后台-1-异常] | [后台-2-异常]"
            debug_name: "异常队"
            sub_handlers:
              - states: "[悠真-终结技可用]"
                sub_handlers:
                  - states: "[悠真-特殊技可用]"
                    operations:
                      - operation_template: "悠真-强化特殊技"
                      - operation_template: "悠真-终结技"
                      - operation_template: "悠真-闪A四刀"
                  - states: ""
                    operations:
                      - operation_template: "悠真-终结技"
              - states: "[悠真-特殊技可用]"
                operations:
                  - operation_template: "悠真-强化特殊技"
                  - operation_template: "悠真-闪A四刀"
              # 没能量了再打打
              - states: ""
                debug_name: "清空电壶离场"
                operations:
                  - operation_template: "悠真-射箭四刀"  # 清空电壶再下场
                  - op_name: "设置状态"
                    data: ["自定义-速切结束"]

            # 非异常队
          - states: ""
            debug_name: "非异常队"
            sub_handlers:
              - states: "[悠真-终结技可用]"
                operations:
                  - operation_template: "悠真-终结技"
              - states: "[悠真-特殊技可用]"
                operations:
                  - operation_template: "悠真-强化特殊技"
                  - operation_template: "悠真-闪A三刀"
              - states: ""
                debug_name: "清空电壶离场"
                operations:
                  - operation_template: "悠真-射箭三刀"  # 清空电壶再下场
                  - op_name: "设置状态"
                    data: ["自定义-速切结束"]
                  - operation_template: "悠真-普通攻击"

      - states: "![后台-1-击破] & ![后台-2-击破]"
        debug_name: "击破队"
        sub_handlers:
          # 后台如果有击破，不管什么队友都留能量
          - states: "[悠真-能量]{110, 120}"
            operations:
              - operation_template: "悠真-强化特殊技"
              - operation_template: "悠真-闪A四刀"

          - states: ""
            debug_name: "清空电壶离场"
            operations:
              - operation_template: "悠真-射箭三刀"  # 清空电壶再下场
              - op_name: "设置状态"
                data: ["自定义-速切结束"]
              - operation_template: "悠真-普通攻击"
