handlers:
  - states: "[前台-可琳]"
    sub_handlers:
      - states: "[自定义-连携换人, 0, 1]"
        operations:
          - op_name: "清除状态"
            state: "自定义-可琳-这次不打特殊技"
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 20

      - states: "[按键可用-终结技] & [自定义-连携换人, 0, 10]"
        operations:
          - op_name: "按键-终结技"
            post_delay: 0.1
            repeat: 10
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 30

      # 可以用特殊技就持续按
      - states: "[按键可用-特殊攻击] & [自定义-连携换人, 2, 10] & ![自定义-可琳-这次不打特殊技, 0, 12]"
        operations:
          - op_name: "设置状态"
            state: "自定义-可琳-这次不打特殊技"
          - op_name: "按键-特殊攻击"
            post_delay: 0.1
            repeat: 40

      - states: "[自定义-连携换人, 10, 999] & [自定义-可琳-这次不打特殊技, 0, 12]"
        operations:
          - op_name: "清除状态"
            state: "自定义-可琳-这次不打特殊技"

      # 没有特殊技可以用
      - states: "![按键可用-特殊攻击]"
        operations:
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 35