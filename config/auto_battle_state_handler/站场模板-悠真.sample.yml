handlers:
  - states: "[前台-悠真]"
    sub_handlers:
      - states: "[按键-切换角色-下一个, 0, 0.4]"
        operations:
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 4
      - states: "[自定义-连携换人, 0, 2]"
        operations:
          - op_name: "等待秒数"
            data: ["0.5"]
          - op_name: "设置状态"
            state: "自定义-动作不打断"
          - op_name: "按键-普通攻击"
            way: "按下"
            press: 1.2
          - op_name: "按键-普通攻击"
            way: "松开"
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 20
          - op_name: "清除状态"
            state: "自定义-动作不打断"
      - states: "[按键可用-特殊攻击]"
        operations:
          - op_name: "等待秒数"
            data: ["0.4"]
          - op_name: "设置状态"
            state: "自定义-动作不打断"
          - op_name: "按键-特殊攻击"
            way: "按下"
            press: 2
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 20
          - op_name: "清除状态"
            state: "自定义-动作不打断"
      - states: "[按键可用-终结技] & [自定义-连携换人, 0, 10]"
        operations:
          - op_name: "设置状态"
            state: "自定义-动作不打断"
          - op_name: "按键-终结技"
            post_delay: 2
            repeat: 2
          - op_name: "清除状态"
            state: "自定义-动作不打断"
      - states: "![按键可用-特殊攻击]"
        operations:
          - op_name: "按键-普通攻击"
            way: "按下"
            press: 1.2
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 25