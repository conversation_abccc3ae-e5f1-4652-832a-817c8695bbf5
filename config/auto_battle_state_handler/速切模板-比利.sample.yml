template_name: "速切模板-比利"
handlers:
  - states: "[前台-比利]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "通用-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "通用-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "通用-连携攻击"

      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      # 失衡时刻
      - states: "[自定义-连携换人, 0, 10]"
        sub_handlers:
          # 失衡的时候就不用考虑这么多了，射吧
          - states: "[比利-终结技可用]"
            operations:
              - operation_template: "通用-终结技"
          # 朴实无华
          - states: "[比利-特殊技可用]"
            operations:
              - op_name: "设置状态"
                state: "自定义-动作不打断"
                seconds: 0.6
              - op_name: "按键-特殊攻击"
                post_delay: 0.3
                repeat: 2

          - states: ""
            operations:
              - op_name: "按键-普通攻击"
                post_delay: 0.1
                repeat: 25

      - states: "![自定义-连携换人, 0, 10]"
        sub_handlers:
          - states: "[比利-特殊技可用]"
            operations:
              - op_name: "按键-特殊攻击"
                post_delay: 0.3
                repeat: 2
              - op_name: "设置状态"
                data: ["自定义-速切结束"]
          - states: ""
            operations:
              - op_name: "按键-普通攻击"
                post_delay: 0.1
                repeat: 25
              - op_name: "设置状态"
                data: ["自定义-速切结束"]