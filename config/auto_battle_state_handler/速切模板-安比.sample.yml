template_name: "速切模板-安比"
handlers:
  - states: "[前台-安比]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        operations:
          - operation_template: "安比-支援攻击"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "通用-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "通用-连携攻击"

      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[安比-终结技可用] & ![自定义-连携换人, 0, 10]"
        operations:
          - operation_template: "通用-终结技"

      - states: "[自定义-黄光切人, 0, 5]"
        sub_handlers:
          - states: "[安比-特殊技可用]"
            operations:
              - op_name: "按键-闪避"
                post_delay: 0.1
              - op_name: "按键-普通攻击"
                post_delay: 0.1
                repeat: 15
              - op_name: "按键-特殊攻击"
                post_delay: 0.2
                repeat: 4
              - op_name: "设置状态"
                data: ["自定义-速切结束"]

          - states: ""
            operations:
              - op_name: "按键-闪避"
                post_delay: 0.1
              - op_name: "按键-普通攻击"
                post_delay: 0.1
                repeat: 12
              - op_name: "按键-特殊攻击"
                way: "松开"
              - op_name: "等待秒数"
                seconds: 0.1
              - op_name: "按键-普通攻击"
                way: "按下"
                press: 0.8
              - op_name: "设置状态"
                data: ["自定义-速切结束"]

      - states: "[安比-特殊技可用]"
        operations:
          - op_name: "按键-闪避"
            post_delay: 0.1
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 15
          - op_name: "按键-特殊攻击"
            post_delay: 0.2
            repeat: 4
          - op_name: "等待秒数"
            seconds: 0.5
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
          - op_name: "等待秒数"
            seconds: 0.5

      - states: ""
        operations:
          - op_name: "按键-闪避"
            post_delay: 0.1
          - op_name: "按键-普通攻击"
            post_delay: 0.1
            repeat: 12
          - op_name: "按键-特殊攻击"
            way: "松开"
          - op_name: "等待秒数"
            seconds: 0.1
          - op_name: "按键-普通攻击"
            way: "按下"
            press: 0.8
          - op_name: "等待秒数"
            seconds: 0.5
          - op_name: "设置状态"
            data: ["自定义-速切结束"]
          - op_name: "等待秒数"
            seconds: 0.5