template_name: "速切模板-赛斯"
handlers:
  - states: "[前台-赛斯]"
    sub_handlers:
      - states: "[自定义-黄光切人, 0, 1]"
        sub_handlers:
          - state_template: "支援攻击模板-全角色"

      - states: "[自定义-红光闪避, 0, 1]"
        operations:
          - operation_template: "通用-闪A"

      - states: "[按键可用-连携技, 0, 0.5]"
        operations:
          - operation_template: "通用-连携攻击"

      # 防呆
      - states: "([按键-切换角色-下一个, 0, 0.3]|[按键-切换角色-上一个, 0, 0.3])"
        debug_name: "切人后等待"
        sub_handlers:
          - states: "[按键可用-快速支援, 0, 0.5]"
            operations:
              - op_name: "等待秒数"
                seconds: 1.0
          - states: ""
            operations:
              - op_name: "等待秒数"
                seconds: 0.3

      - states: "[赛斯-终结技可用] & [自定义-血量扣减, 0, 2] "
        operations:
          - operation_template: "通用-终结技"  # 时长一样

      - states: "[赛斯-意气]{95, 101}"
        operations:
          - op_name: "设置状态"
            state: "自定义-赛斯-意气风发"
          - op_name: "按键-普通攻击"
            way: "按下"
            press: 2.2

      - states: "[赛斯-特殊技可用]"
        operations:
          - op_name: "按键-特殊攻击"
            way: "按下"
            press: 0.4
          - operation_template: "通用-切人普通攻击"

      - states: ""
        operations:
          - operation_template: "通用-切人普通攻击"