import sys
from unittest import mock
from pprint import PrettyPrinter

sys.modules["evdev.ecodes"] = mock.Mock()
from evdev import ecodes_runtime as ecodes

pprint = PrettyPrinter(indent=2, sort_dicts=True, width=120).pprint


print("# Automatically generated by evdev.genecodes_py")
print()
print('"""')
print(ecodes.__doc__.strip())
print('"""')

print()
print("from typing import Final, Dict, Tuple, Union")
print()

for name, value in ecodes.ecodes.items():
    print(f"{name}: Final[int] = {value}")
print()

entries = [
    ("ecodes", "Dict[str, int]", "#: Mapping of names to values."),
    ("bytype", "Dict[int, Dict[int, Union[str, Tuple[str]]]]", "#: Mapping of event types to other value/name mappings."),
    ("keys",   "Dict[int, Union[str, Tuple[str]]]", "#: Keys are a combination of all BTN and KEY codes."),
    ("KEY",    "Dict[int, Union[str, Tuple[str]]]", None),
    ("ABS",    "Dict[int, Union[str, Tuple[str]]]", None),
    ("REL",    "Dict[int, Union[str, Tuple[str]]]", None),
    ("SW",     "Dict[int, Union[str, Tuple[str]]]", None),
    ("MSC",    "Dict[int, Union[str, Tuple[str]]]", None),
    ("LED",    "Dict[int, Union[str, Tuple[str]]]", None),
    ("BTN",    "Dict[int, Union[str, Tuple[str]]]", None),
    ("REP",    "Dict[int, Union[str, Tuple[str]]]", None),
    ("SND",    "Dict[int, Union[str, Tuple[str]]]", None),
    ("ID",     "Dict[int, Union[str, Tuple[str]]]", None),
    ("EV",     "Dict[int, Union[str, Tuple[str]]]", None),
    ("BUS",    "Dict[int, Union[str, Tuple[str]]]", None),
    ("SYN",    "Dict[int, Union[str, Tuple[str]]]", None),
    ("FF",     "Dict[int, Union[str, Tuple[str]]]", None),
    ("UI_FF",  "Dict[int, Union[str, Tuple[str]]]", None),
    ("FF_STATUS",  "Dict[int, Union[str, Tuple[str]]]", None),
    ("INPUT_PROP", "Dict[int, Union[str, Tuple[str]]]", None)
]

for key, annotation, doc in entries:
    if doc:
        print(doc)

    print(f"{key}: {annotation} = ", end="")
    pprint(getattr(ecodes, key))
    print()