XK_ISO_Lock = 0xFE01
XK_ISO_Level2_Latch = 0xFE02
XK_ISO_Level3_Shift = 0xFE03
XK_ISO_Level3_Latch = 0xFE04
XK_ISO_Level3_Lock = 0xFE05
XK_ISO_Group_Shift = 0xFF7E
XK_ISO_Group_Latch = 0xFE06
XK_ISO_Group_Lock = 0xFE07
XK_ISO_Next_Group = 0xFE08
XK_ISO_Next_Group_Lock = 0xFE09
XK_ISO_Prev_Group = 0xFE0A
XK_ISO_Prev_Group_Lock = 0xFE0B
XK_ISO_First_Group = 0xFE0C
XK_ISO_First_Group_Lock = 0xFE0D
XK_ISO_Last_Group = 0xFE0E
XK_ISO_Last_Group_Lock = 0xFE0F
XK_ISO_Left_Tab = 0xFE20
XK_ISO_Move_Line_Up = 0xFE21
XK_ISO_Move_Line_Down = 0xFE22
XK_ISO_Partial_Line_Up = 0xFE23
XK_ISO_Partial_Line_Down = 0xFE24
XK_ISO_Partial_Space_Left = 0xFE25
XK_ISO_Partial_Space_Right = 0xFE26
XK_ISO_Set_Margin_Left = 0xFE27
XK_ISO_Set_Margin_Right = 0xFE28
XK_ISO_Release_Margin_Left = 0xFE29
XK_ISO_Release_Margin_Right = 0xFE2A
XK_ISO_Release_Both_Margins = 0xFE2B
XK_ISO_Fast_Cursor_Left = 0xFE2C
XK_ISO_Fast_Cursor_Right = 0xFE2D
XK_ISO_Fast_Cursor_Up = 0xFE2E
XK_ISO_Fast_Cursor_Down = 0xFE2F
XK_ISO_Continuous_Underline = 0xFE30
XK_ISO_Discontinuous_Underline = 0xFE31
XK_ISO_Emphasize = 0xFE32
XK_ISO_Center_Object = 0xFE33
XK_ISO_Enter = 0xFE34
XK_dead_grave = 0xFE50
XK_dead_acute = 0xFE51
XK_dead_circumflex = 0xFE52
XK_dead_tilde = 0xFE53
XK_dead_macron = 0xFE54
XK_dead_breve = 0xFE55
XK_dead_abovedot = 0xFE56
XK_dead_diaeresis = 0xFE57
XK_dead_abovering = 0xFE58
XK_dead_doubleacute = 0xFE59
XK_dead_caron = 0xFE5A
XK_dead_cedilla = 0xFE5B
XK_dead_ogonek = 0xFE5C
XK_dead_iota = 0xFE5D
XK_dead_voiced_sound = 0xFE5E
XK_dead_semivoiced_sound = 0xFE5F
XK_dead_belowdot = 0xFE60
XK_First_Virtual_Screen = 0xFED0
XK_Prev_Virtual_Screen = 0xFED1
XK_Next_Virtual_Screen = 0xFED2
XK_Last_Virtual_Screen = 0xFED4
XK_Terminate_Server = 0xFED5
XK_AccessX_Enable = 0xFE70
XK_AccessX_Feedback_Enable = 0xFE71
XK_RepeatKeys_Enable = 0xFE72
XK_SlowKeys_Enable = 0xFE73
XK_BounceKeys_Enable = 0xFE74
XK_StickyKeys_Enable = 0xFE75
XK_MouseKeys_Enable = 0xFE76
XK_MouseKeys_Accel_Enable = 0xFE77
XK_Overlay1_Enable = 0xFE78
XK_Overlay2_Enable = 0xFE79
XK_AudibleBell_Enable = 0xFE7A
XK_Pointer_Left = 0xFEE0
XK_Pointer_Right = 0xFEE1
XK_Pointer_Up = 0xFEE2
XK_Pointer_Down = 0xFEE3
XK_Pointer_UpLeft = 0xFEE4
XK_Pointer_UpRight = 0xFEE5
XK_Pointer_DownLeft = 0xFEE6
XK_Pointer_DownRight = 0xFEE7
XK_Pointer_Button_Dflt = 0xFEE8
XK_Pointer_Button1 = 0xFEE9
XK_Pointer_Button2 = 0xFEEA
XK_Pointer_Button3 = 0xFEEB
XK_Pointer_Button4 = 0xFEEC
XK_Pointer_Button5 = 0xFEED
XK_Pointer_DblClick_Dflt = 0xFEEE
XK_Pointer_DblClick1 = 0xFEEF
XK_Pointer_DblClick2 = 0xFEF0
XK_Pointer_DblClick3 = 0xFEF1
XK_Pointer_DblClick4 = 0xFEF2
XK_Pointer_DblClick5 = 0xFEF3
XK_Pointer_Drag_Dflt = 0xFEF4
XK_Pointer_Drag1 = 0xFEF5
XK_Pointer_Drag2 = 0xFEF6
XK_Pointer_Drag3 = 0xFEF7
XK_Pointer_Drag4 = 0xFEF8
XK_Pointer_Drag5 = 0xFEFD
XK_Pointer_EnableKeys = 0xFEF9
XK_Pointer_Accelerate = 0xFEFA
XK_Pointer_DfltBtnNext = 0xFEFB
XK_Pointer_DfltBtnPrev = 0xFEFC
