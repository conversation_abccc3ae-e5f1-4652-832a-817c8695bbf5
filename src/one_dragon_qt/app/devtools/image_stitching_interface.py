import cv2
import numpy as np
from PySide6.QtCore import Qt
from PySide6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel, 
                               QSpinBox, QFileDialog, QMessageBox, QComboBox)
from qfluentwidgets import FluentI<PERSON>, PushButton

from one_dragon.base.operation.one_dragon_context import OneDragonContext
from one_dragon.utils.log_utils import log
from one_dragon.utils import cv2_utils
from one_dragon_qt.widgets.image_viewer_widget import ImageViewerWidget
from one_dragon_qt.widgets.setting_card.multi_push_setting_card import MultiPushSettingCard
from one_dragon_qt.widgets.vertical_scroll_interface import VerticalScrollInterface


class ImageStitchingInterface(VerticalScrollInterface):
    """图片拼接界面"""

    def __init__(self, ctx: OneDragonContext, parent=None):
        self.ctx: OneDragonContext = ctx
        
        VerticalScrollInterface.__init__(
            self,
            content_widget=None,
            object_name='image_stitching_interface',
            nav_text_cn='图片拼接',
            parent=parent,
        )
        
        # 图像数据
        self.base_image: np.ndarray | None = None  # 底图
        self.second_image: np.ndarray | None = None  # 第二张图
        self.merged_image: np.ndarray | None = None  # 合并后的图像
        
        # 拼接参数
        self.stitch_direction: str = 'left'  # 拼接方向: left, right, top, bottom
        self.offset_x: int = 0  # 横向偏移
        self.offset_y: int = 0  # 纵向偏移
        self.overlap_ratio: float = 0.9  # 重叠区域高度/宽度比例

    def get_content_widget(self) -> QWidget:
        """获取内容组件"""
        # 主容器，水平布局
        main_widget = QWidget()
        main_layout = QHBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(12)

        main_layout.addWidget(self._init_control_panel())
        main_layout.addWidget(self._init_image_display_panel(), stretch=1)

        return main_widget

    def _init_control_panel(self) -> QWidget:
        """初始化控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(12)

        # 图像加载
        self.load_base_btn = PushButton(text='加载底图')
        self.load_base_btn.clicked.connect(self.on_load_base_clicked)
        
        self.load_second_btn = PushButton(text='加载第二张图')
        self.load_second_btn.clicked.connect(self.on_load_second_clicked)
        
        load_card = MultiPushSettingCard(
            icon=FluentIcon.FOLDER,
            title='图像加载',
            btn_list=[self.load_base_btn, self.load_second_btn]
        )
        control_layout.addWidget(load_card)

        # 拼接方向
        self.direction_combo = QComboBox()
        self.direction_combo.addItems(['左边', '右边', '上边', '下边'])
        self.direction_combo.currentTextChanged.connect(self.on_direction_changed)
        
        direction_card = MultiPushSettingCard(
            icon=FluentIcon.ALIGNMENT,
            title='拼接方向',
            content='第二张图相对于底图的位置',
            btn_list=[self.direction_combo]
        )
        control_layout.addWidget(direction_card)

        # 重叠比例
        self.overlap_input = QSpinBox()
        self.overlap_input.setRange(50, 100)
        self.overlap_input.setValue(90)
        self.overlap_input.setSuffix('%')
        self.overlap_input.valueChanged.connect(self.on_overlap_changed)
        
        overlap_card = MultiPushSettingCard(
            icon=FluentIcon.ZOOM,
            title='重叠比例',
            content='用于匹配的区域大小',
            btn_list=[self.overlap_input]
        )
        control_layout.addWidget(overlap_card)

        # 自动匹配
        self.auto_match_btn = PushButton(text='自动匹配')
        self.auto_match_btn.clicked.connect(self.on_auto_match_clicked)
        
        match_card = MultiPushSettingCard(
            icon=FluentIcon.SEARCH,
            title='图像匹配',
            btn_list=[self.auto_match_btn]
        )
        control_layout.addWidget(match_card)

        # 手动调整
        self.offset_x_input = QSpinBox()
        self.offset_x_input.setRange(-9999, 9999)
        self.offset_x_input.setValue(0)
        self.offset_x_input.valueChanged.connect(self.on_offset_changed)
        
        self.offset_y_input = QSpinBox()
        self.offset_y_input.setRange(-9999, 9999)
        self.offset_y_input.setValue(0)
        self.offset_y_input.valueChanged.connect(self.on_offset_changed)
        
        self.apply_offset_btn = PushButton(text='应用偏移')
        self.apply_offset_btn.clicked.connect(self.on_apply_offset_clicked)
        
        offset_card = MultiPushSettingCard(
            icon=FluentIcon.MOVE,
            title='手动调整',
            content='微调第二张图的位置',
            btn_list=[
                QLabel('X:'), self.offset_x_input,
                QLabel('Y:'), self.offset_y_input,
                self.apply_offset_btn
            ]
        )
        control_layout.addWidget(offset_card)

        # 合并操作
        self.merge_btn = PushButton(text='合并图像')
        self.merge_btn.clicked.connect(self.on_merge_clicked)
        
        self.save_btn = PushButton(text='保存结果')
        self.save_btn.clicked.connect(self.on_save_clicked)
        
        merge_card = MultiPushSettingCard(
            icon=FluentIcon.SAVE,
            title='合并操作',
            btn_list=[self.merge_btn, self.save_btn]
        )
        control_layout.addWidget(merge_card)

        control_layout.addStretch(1)
        return control_widget

    def _init_image_display_panel(self) -> QWidget:
        """初始化图像显示面板"""
        display_widget = QWidget()
        display_layout = QVBoxLayout(display_widget)
        display_layout.setContentsMargins(0, 0, 0, 0)
        display_layout.setSpacing(12)

        # 图像显示器
        self.image_viewer = ImageViewerWidget()
        display_layout.addWidget(self.image_viewer)

        return display_widget

    def on_load_base_clicked(self):
        """加载底图"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, '选择底图', '', 
            'Image Files (*.png *.jpg *.jpeg *.bmp *.tiff)'
        )
        
        if file_path:
            try:
                self.base_image = cv2_utils.read_image(file_path)
                if self.base_image is None:
                    raise ValueError("无法读取图像文件")

                log.info(f'加载底图成功: {file_path}')
                self._update_display()
                self._update_button_states()

            except Exception as e:
                QMessageBox.warning(self, '错误', f'加载底图失败: {str(e)}')

    def on_load_second_clicked(self):
        """加载第二张图"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, '选择第二张图', '', 
            'Image Files (*.png *.jpg *.jpeg *.bmp *.tiff)'
        )
        
        if file_path:
            try:
                self.second_image = cv2_utils.read_image(file_path)
                if self.second_image is None:
                    raise ValueError("无法读取图像文件")

                log.info(f'加载第二张图成功: {file_path}')
                self._update_display()
                self._update_button_states()

            except Exception as e:
                QMessageBox.warning(self, '错误', f'加载第二张图失败: {str(e)}')

    def on_direction_changed(self, text: str):
        """拼接方向改变"""
        direction_map = {
            '左边': 'left',
            '右边': 'right', 
            '上边': 'top',
            '下边': 'bottom'
        }
        self.stitch_direction = direction_map.get(text, 'left')
        log.info(f'拼接方向设置为: {text}')

    def on_overlap_changed(self, value: int):
        """重叠比例改变"""
        self.overlap_ratio = value / 100.0
        log.info(f'重叠比例设置为: {value}%')

    def on_auto_match_clicked(self):
        """自动匹配"""
        if self.base_image is None or self.second_image is None:
            QMessageBox.warning(self, '错误', '请先加载两张图像')
            return
        
        try:
            # 执行图像匹配
            offset_x, offset_y = self._perform_image_matching()
            
            # 更新偏移值
            self.offset_x = offset_x
            self.offset_y = offset_y
            self.offset_x_input.setValue(offset_x)
            self.offset_y_input.setValue(offset_y)
            
            # 更新显示
            self._update_display()
            
            log.info(f'自动匹配完成，偏移: ({offset_x}, {offset_y})')
            
        except Exception as e:
            QMessageBox.warning(self, '错误', f'自动匹配失败: {str(e)}')
            log.error(f'自动匹配失败: {str(e)}')

    def on_offset_changed(self):
        """偏移值改变"""
        self.offset_x = self.offset_x_input.value()
        self.offset_y = self.offset_y_input.value()

    def on_apply_offset_clicked(self):
        """应用偏移"""
        self._update_display()
        log.info(f'应用偏移: ({self.offset_x}, {self.offset_y})')

    def on_merge_clicked(self):
        """合并图像"""
        if self.base_image is None or self.second_image is None:
            QMessageBox.warning(self, '错误', '请先加载两张图像')
            return
        
        try:
            # 执行图像合并
            self.merged_image = self._merge_images()
            
            # 将合并后的图像设为新的底图
            self.base_image = self.merged_image.copy()
            self.second_image = None
            
            # 重置偏移
            self.offset_x = 0
            self.offset_y = 0
            self.offset_x_input.setValue(0)
            self.offset_y_input.setValue(0)
            
            # 更新显示
            self._update_display()
            self._update_button_states()
            
            log.info('图像合并完成')
            
        except Exception as e:
            QMessageBox.warning(self, '错误', f'图像合并失败: {str(e)}')
            log.error(f'图像合并失败: {str(e)}')

    def on_save_clicked(self):
        """保存结果"""
        if self.base_image is None:
            QMessageBox.warning(self, '错误', '没有可保存的图像')
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, '保存图像', '', 
            'PNG Files (*.png);;JPG Files (*.jpg);;All Files (*)'
        )
        
        if file_path:
            try:
                cv2_utils.save_image(self.base_image, file_path)
                log.info(f'图像保存成功: {file_path}')
                QMessageBox.information(self, '成功', '图像保存成功')

            except Exception as e:
                QMessageBox.warning(self, '错误', f'保存失败: {str(e)}')

    def _update_display(self):
        """更新图像显示"""
        if self.base_image is None:
            return
        
        # 如果有第二张图，显示重叠效果
        if self.second_image is not None:
            display_image = self._create_overlay_image()
        else:
            display_image = self.base_image.copy()
        
        # 图像已经是RGB格式，直接显示
        self.image_viewer.set_image(display_image)

    def _update_button_states(self):
        """更新按钮状态"""
        has_base = self.base_image is not None
        has_second = self.second_image is not None
        has_both = has_base and has_second
        
        self.auto_match_btn.setEnabled(has_both)
        self.apply_offset_btn.setEnabled(has_both)
        self.merge_btn.setEnabled(has_both)
        self.save_btn.setEnabled(has_base)

    def _perform_image_matching(self) -> tuple[int, int]:
        """执行图像匹配，返回偏移量"""
        if self.base_image is None or self.second_image is None:
            return 0, 0

        # 根据拼接方向提取匹配区域
        base_region, second_region = self._extract_matching_regions()

        if base_region is None or second_region is None:
            raise ValueError("无法提取匹配区域")

        # 使用模板匹配
        result = cv2.matchTemplate(base_region, second_region, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, max_loc = cv2.minMaxLoc(result)

        if max_val < 0.3:  # 匹配度阈值
            raise ValueError(f"匹配度过低: {max_val:.3f}")

        # 计算偏移量
        offset_x, offset_y = self._calculate_offset_from_match(max_loc)

        log.info(f'匹配成功，匹配度: {max_val:.3f}')
        return offset_x, offset_y

    def _extract_matching_regions(self) -> tuple[np.ndarray | None, np.ndarray | None]:
        """提取用于匹配的区域"""
        if self.base_image is None or self.second_image is None:
            return None, None

        base_h, base_w = self.base_image.shape[:2]
        second_h, second_w = self.second_image.shape[:2]

        if self.stitch_direction == 'left':
            # 第二张图在左边，取第二张图的右边部分和底图的左边部分
            overlap_w = int(min(base_w, second_w) * self.overlap_ratio)
            overlap_h = int(min(base_h, second_h) * self.overlap_ratio)

            # 计算垂直居中的起始位置
            base_start_y = max(0, (base_h - overlap_h) // 2)
            second_start_y = max(0, (second_h - overlap_h) // 2)

            base_region = self.base_image[base_start_y:base_start_y + overlap_h, :overlap_w]
            second_region = self.second_image[second_start_y:second_start_y + overlap_h, second_w - overlap_w:]

        elif self.stitch_direction == 'right':
            # 第二张图在右边，取第二张图的左边部分和底图的右边部分
            overlap_w = int(min(base_w, second_w) * self.overlap_ratio)
            overlap_h = int(min(base_h, second_h) * self.overlap_ratio)

            base_start_y = max(0, (base_h - overlap_h) // 2)
            second_start_y = max(0, (second_h - overlap_h) // 2)

            base_region = self.base_image[base_start_y:base_start_y + overlap_h, base_w - overlap_w:]
            second_region = self.second_image[second_start_y:second_start_y + overlap_h, :overlap_w]

        elif self.stitch_direction == 'top':
            # 第二张图在上边，取第二张图的下边部分和底图的上边部分
            overlap_w = int(min(base_w, second_w) * self.overlap_ratio)
            overlap_h = int(min(base_h, second_h) * self.overlap_ratio)

            base_start_x = max(0, (base_w - overlap_w) // 2)
            second_start_x = max(0, (second_w - overlap_w) // 2)

            base_region = self.base_image[:overlap_h, base_start_x:base_start_x + overlap_w]
            second_region = self.second_image[second_h - overlap_h:, second_start_x:second_start_x + overlap_w]

        elif self.stitch_direction == 'bottom':
            # 第二张图在下边，取第二张图的上边部分和底图的下边部分
            overlap_w = int(min(base_w, second_w) * self.overlap_ratio)
            overlap_h = int(min(base_h, second_h) * self.overlap_ratio)

            base_start_x = max(0, (base_w - overlap_w) // 2)
            second_start_x = max(0, (second_w - overlap_w) // 2)

            base_region = self.base_image[base_h - overlap_h:, base_start_x:base_start_x + overlap_w]
            second_region = self.second_image[:overlap_h, second_start_x:second_start_x + overlap_w]

        else:
            return None, None

        return base_region, second_region

    def _calculate_offset_from_match(self, match_loc: tuple[int, int]) -> tuple[int, int]:
        """根据匹配位置计算偏移量"""
        match_x, match_y = match_loc

        if self.stitch_direction in ['left', 'right']:
            # 水平拼接，主要关注垂直偏移
            offset_x = 0
            offset_y = match_y
        else:
            # 垂直拼接，主要关注水平偏移
            offset_x = match_x
            offset_y = 0

        return offset_x, offset_y

    def _create_overlay_image(self) -> np.ndarray:
        """创建重叠显示的图像"""
        if self.base_image is None or self.second_image is None:
            return self.base_image

        base_h, base_w = self.base_image.shape[:2]
        second_h, second_w = self.second_image.shape[:2]

        # 计算第二张图的位置
        if self.stitch_direction == 'left':
            second_x = -second_w + self.offset_x
            second_y = self.offset_y
        elif self.stitch_direction == 'right':
            second_x = base_w + self.offset_x
            second_y = self.offset_y
        elif self.stitch_direction == 'top':
            second_x = self.offset_x
            second_y = -second_h + self.offset_y
        elif self.stitch_direction == 'bottom':
            second_x = self.offset_x
            second_y = base_h + self.offset_y
        else:
            second_x = self.offset_x
            second_y = self.offset_y

        # 计算合并后的画布大小
        min_x = min(0, second_x)
        min_y = min(0, second_y)
        max_x = max(base_w, second_x + second_w)
        max_y = max(base_h, second_y + second_h)

        canvas_w = max_x - min_x
        canvas_h = max_y - min_y

        # 创建画布
        canvas = np.zeros((canvas_h, canvas_w, 3), dtype=np.uint8)

        # 放置底图
        base_start_x = -min_x
        base_start_y = -min_y
        canvas[base_start_y:base_start_y + base_h, base_start_x:base_start_x + base_w] = self.base_image

        # 放置第二张图（半透明）
        second_start_x = second_x - min_x
        second_start_y = second_y - min_y

        # 确保坐标在画布范围内
        if (second_start_x < canvas_w and second_start_y < canvas_h and
            second_start_x + second_w > 0 and second_start_y + second_h > 0):

            # 计算实际的重叠区域
            x1 = max(0, second_start_x)
            y1 = max(0, second_start_y)
            x2 = min(canvas_w, second_start_x + second_w)
            y2 = min(canvas_h, second_start_y + second_h)

            # 计算在第二张图中的对应区域
            src_x1 = x1 - second_start_x
            src_y1 = y1 - second_start_y
            src_x2 = src_x1 + (x2 - x1)
            src_y2 = src_y1 + (y2 - y1)

            # 半透明叠加
            alpha = 0.5
            canvas[y1:y2, x1:x2] = (
                canvas[y1:y2, x1:x2] * (1 - alpha) +
                self.second_image[src_y1:src_y2, src_x1:src_x2] * alpha
            ).astype(np.uint8)

        return canvas

    def _merge_images(self) -> np.ndarray:
        """合并两张图像"""
        if self.base_image is None or self.second_image is None:
            return self.base_image

        base_h, base_w = self.base_image.shape[:2]
        second_h, second_w = self.second_image.shape[:2]

        # 计算第二张图的位置（与_create_overlay_image相同的逻辑）
        if self.stitch_direction == 'left':
            second_x = -second_w + self.offset_x
            second_y = self.offset_y
        elif self.stitch_direction == 'right':
            second_x = base_w + self.offset_x
            second_y = self.offset_y
        elif self.stitch_direction == 'top':
            second_x = self.offset_x
            second_y = -second_h + self.offset_y
        elif self.stitch_direction == 'bottom':
            second_x = self.offset_x
            second_y = base_h + self.offset_y
        else:
            second_x = self.offset_x
            second_y = self.offset_y

        # 计算合并后的画布大小
        min_x = min(0, second_x)
        min_y = min(0, second_y)
        max_x = max(base_w, second_x + second_w)
        max_y = max(base_h, second_y + second_h)

        canvas_w = max_x - min_x
        canvas_h = max_y - min_y

        # 创建画布
        canvas = np.zeros((canvas_h, canvas_w, 3), dtype=np.uint8)

        # 放置底图
        base_start_x = -min_x
        base_start_y = -min_y
        canvas[base_start_y:base_start_y + base_h, base_start_x:base_start_x + base_w] = self.base_image

        # 放置第二张图（完全覆盖）
        second_start_x = second_x - min_x
        second_start_y = second_y - min_y

        if (second_start_x < canvas_w and second_start_y < canvas_h and
            second_start_x + second_w > 0 and second_start_y + second_h > 0):

            x1 = max(0, second_start_x)
            y1 = max(0, second_start_y)
            x2 = min(canvas_w, second_start_x + second_w)
            y2 = min(canvas_h, second_start_y + second_h)

            src_x1 = x1 - second_start_x
            src_y1 = y1 - second_start_y
            src_x2 = src_x1 + (x2 - x1)
            src_y2 = src_y1 + (y2 - y1)

            canvas[y1:y2, x1:x2] = self.second_image[src_y1:src_y2, src_x1:src_x2]

        return canvas
