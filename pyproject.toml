[project]
name = "ZenlessZoneZero-OneDragon"
version = "2.0.0"
description = "绝区零 一条龙 | 全自动 | 自动闪避 | 自动每日 | 自动空洞 | 支持手柄"
requires-python = ">=3.11.9,<=3.11.12"
dependencies = [
    "pyside6==*******",
    "pyside6-fluent-widgets==1.7.0",
    "pyyaml==6.0.1",
    "opencv-python==*********",
    "pyautogui==0.9.54",
    "pynput==1.7.7",
    "onnxruntime-directml==1.18.0",
    "mss==9.0.1",
    "shapely==2.0.4",
    "pyclipper==1.3.0.post5",
    "soundcard==0.4.3",
    "librosa==0.10.2.post1",
    "gensim==4.3.3",
    "posthog>=3.0.0",
]

[dependency-groups]
dev = [
    "colorama==0.4.6",
    "matplotlib==3.10.3",
    "polib==1.2.0",
    "pyinstaller==6.7.0",
]

[tool.uv]
package = false
cache-dir = "./.install/uv_cache"
default-groups = []
